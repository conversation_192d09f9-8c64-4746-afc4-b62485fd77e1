{"name": "is-plain-object", "description": "Returns true if an object was created by the `Object` constructor.", "version": "3.0.1", "homepage": "https://github.com/jonschlinkert/is-plain-object", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON> (http://twitter.com/jonschlink<PERSON>)", "<PERSON><PERSON> (http://onokumus.com)", "<PERSON> (https://svachon.com)", "(https://github.com/wtgtybhertgeghgtwtg)", "<PERSON><PERSON><PERSON> (https://github.com/TrySound)"], "repository": "jonschlinkert/is-plain-object", "bugs": {"url": "https://github.com/jonschlinkert/is-plain-object/issues"}, "license": "MIT", "main": "index.cjs.js", "module": "index.es.js", "types": "index.d.ts", "files": ["index.d.ts", "index.es.js", "index.cjs.js"], "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test_browser": "mocha-headless-chrome --args=disable-web-security -f test/browser.html", "test_node": "mocha -r esm", "test": "npm run test_node && npm run build && npm run test_browser", "prepare": "rollup -c"}, "devDependencies": {"@rollup/plugin-node-resolve": "^8.1.0", "chai": "^4.2.0", "esm": "^3.2.22", "gulp-format-md": "^1.0.0", "isobject": "^4.0.0", "mocha": "^6.1.4", "mocha-headless-chrome": "^3.1.0", "rollup": "^1.10.1"}, "keywords": ["check", "is", "is-object", "isobject", "javascript", "kind", "kind-of", "object", "plain", "type", "typeof", "value"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-number", "isobject", "kind-of"]}, "lint": {"reflinks": true}}}