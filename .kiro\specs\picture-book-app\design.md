# 绘本阅读小程序 - 设计文档

**版本**: 1.0  
**日期**: 2025-08-01

## 1. 系统架构概述

### 1.1 技术栈选择

**前端技术栈**:
- **框架**: uniapp + unibest 脚手架
- **UI组件库**: Wot UI (适配微信小程序)
- **状态管理**: Pinia
- **网络请求**: uni.request 封装
- **开发语言**: TypeScript + Vue 3 Composition API

**后端技术栈**:
- **基础框架**: ruoyi-vue-pro (Spring Boot)
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **文件存储**: 阿里云OSS/腾讯云COS
- **音频处理**: 语音识别API (腾讯云/百度AI)

### 1.2 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   微信小程序     │    │   后端服务       │    │   第三方服务     │
│                │    │                │    │                │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 页面层      │ │    │ │ Controller  │ │    │ │ 微信API     │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 组件层      │ │◄──►│ │ Service     │ │◄──►│ │ 语音识别API │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 工具层      │ │    │ │ Repository  │ │    │ │ 文件存储    │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 2. 前端架构设计

### 2.1 项目目录结构

```
src/
├── pages/                    # 页面目录
│   ├── index/               # 首页
│   ├── bookshelf/           # 书架模块
│   │   ├── english/         # 英文书架
│   │   ├── chinese/         # 中文书架
│   │   └── detail/          # 绘本集详情
│   ├── class/               # 班级模块
│   │   ├── index/           # 班级首页
│   │   ├── assignment/      # 作业详情
│   │   └── management/      # 班级管理
│   ├── profile/             # 个人中心
│   ├── checkin/             # 学习打卡
│   └── audio/               # 配音/磨耳朵
├── components/              # 公共组件
│   ├── BookCard/            # 绘本卡片
│   ├── BookReader/          # 绘本阅读器
│   ├── AudioPlayer/         # 音频播放器
│   ├── AssignmentCard/      # 作业卡片
│   ├── CheckinCard/         # 打卡卡片
│   └── UserAvatar/          # 用户头像
├── stores/                  # 状态管理
│   ├── user.ts             # 用户状态
│   ├── class.ts            # 班级状态
│   ├── book.ts             # 图书状态
│   └── audio.ts            # 音频状态
├── utils/                   # 工具函数
│   ├── request.ts          # 网络请求封装
│   ├── auth.ts             # 认证工具
│   ├── storage.ts          # 本地存储
│   └── common.ts           # 通用工具
├── types/                   # TypeScript 类型定义
│   ├── api.ts              # API 接口类型
│   ├── user.ts             # 用户相关类型
│   └── book.ts             # 图书相关类型
└── static/                  # 静态资源
    ├── images/             # 图片资源
    └── icons/              # 图标资源
```

### 2.2 核心组件设计

#### 2.2.1 BookReader 绘本阅读器组件

```typescript
// components/BookReader/BookReader.vue
interface BookReaderProps {
  bookId: string;
  bookData: BookInfo;
  autoPlay?: boolean;
  showControls?: boolean;
}

interface BookReaderEmits {
  onPageChange: (page: number) => void;
  onReadComplete: () => void;
  onAudioEnd: () => void;
}

// 核心功能:
// - 图文同步显示
// - 音频播放控制
// - 页面翻页动画
// - 阅读进度记录
// - 配音录制功能
```

#### 2.2.2 AudioPlayer 音频播放器组件

```typescript
// components/AudioPlayer/AudioPlayer.vue
interface AudioPlayerProps {
  playlist: AudioItem[];
  currentIndex: number;
  showMiniPlayer?: boolean;
}

interface AudioPlayerState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  playMode: 'single' | 'loop' | 'shuffle';
  timerMode: number; // 定时关闭(分钟)
}

// 核心功能:
// - 播放/暂停/上一首/下一首
// - 进度条拖拽
// - 播放模式切换
// - 定时关闭
// - 迷你播放器
```

#### 2.2.3 AssignmentCard 作业卡片组件

```typescript
// components/AssignmentCard/AssignmentCard.vue
interface AssignmentCardProps {
  assignment: AssignmentInfo;
  userRole: 'teacher' | 'student';
  showProgress?: boolean;
}

// 核心功能:
// - 作业信息展示
// - 完成状态显示
// - 进度条展示
// - 角色权限控制
```

### 2.3 状态管理设计

#### 2.3.1 用户状态管理

```typescript
// stores/user.ts
export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null as UserInfo | null,
    isLoggedIn: false,
    stats: null as UserStats | null,
  }),
  
  actions: {
    async login() {
      // 微信授权登录
    },
    
    async getUserInfo() {
      // 获取用户信息
    },
    
    async updateProfile(data: UpdateProfileData) {
      // 更新个人资料
    },
    
    async getUserStats() {
      // 获取用户统计数据
    }
  }
});
```

#### 2.3.2 班级状态管理

```typescript
// stores/class.ts
export const useClassStore = defineStore('class', {
  state: () => ({
    classList: [] as ClassInfo[],
    currentClass: null as ClassInfo | null,
    assignments: [] as AssignmentInfo[],
    members: [] as ClassMember[],
  }),
  
  actions: {
    async getClassList() {
      // 获取班级列表
    },
    
    async switchClass(classId: string) {
      // 切换当前班级
    },
    
    async getAssignments(classId: string) {
      // 获取班级作业
    },
    
    async getClassMembers(classId: string) {
      // 获取班级成员
    }
  }
});
```

## 3. 页面架构设计

### 3.1 页面路由配置

```json
{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "绘本小程序"
      }
    },
    {
      "path": "pages/bookshelf/english/index",
      "style": {
        "navigationBarTitleText": "英文书架"
      }
    },
    {
      "path": "pages/bookshelf/chinese/index", 
      "style": {
        "navigationBarTitleText": "中文书架"
      }
    },
    {
      "path": "pages/class/index/index",
      "style": {
        "navigationBarTitleText": "班级"
      }
    },
    {
      "path": "pages/checkin/index",
      "style": {
        "navigationBarTitleText": "学习打卡"
      }
    },
    {
      "path": "pages/profile/index",
      "style": {
        "navigationBarTitleText": "我的"
      }
    }
  ],
  "tabBar": {
    "list": [
      {
        "pagePath": "pages/bookshelf/english/index",
        "text": "英文书架"
      },
      {
        "pagePath": "pages/bookshelf/chinese/index",
        "text": "中文书架"
      },
      {
        "pagePath": "pages/class/index/index",
        "text": "班级"
      },
      {
        "pagePath": "pages/checkin/index",
        "text": "学习打卡"
      },
      {
        "pagePath": "pages/profile/index",
        "text": "我的"
      }
    ]
  }
}
```

### 3.2 核心页面设计

#### 3.2.1 英文书架页面

```vue
<!-- pages/bookshelf/english/index.vue -->
<template>
  <view class="bookshelf-container">
    <!-- 顶部搜索栏 -->
    <view class="header">
      <wot-button @click="showDrawer = true">目录</wot-button>
      <wot-search v-model="searchKeyword" placeholder="搜索绘本" />
      <wot-button @click="goToAudio">磨耳朵</wot-button>
    </view>
    
    <!-- 分类标签 -->
    <wot-tabs v-model="activeTab" @change="onTabChange">
      <wot-tab title="全部" name="all" />
      <wot-tab title="分级阅读" name="leveled" />
      <wot-tab title="桥梁书" name="bridge" />
      <wot-tab title="章节书" name="chapter" />
    </wot-tabs>
    
    <!-- 内容区域 -->
    <scroll-view class="content" scroll-y>
      <!-- 阅读计划 -->
      <view class="section" v-if="readingPlans.length">
        <view class="section-title">阅读计划</view>
        <view class="plan-list">
          <view 
            class="plan-item" 
            v-for="plan in readingPlans" 
            :key="plan.planId"
            @click="goToPlan(plan)"
          >
            <image :src="plan.coverUrl" class="plan-cover" />
            <view class="plan-info">
              <text class="plan-name">{{ plan.planName }}</text>
              <text class="plan-progress">第{{ plan.currentDay }}天/共{{ plan.totalDays }}天</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 最近阅读 -->
      <view class="section" v-if="recentBooks.length">
        <view class="section-header">
          <text class="section-title">最近阅读</text>
          <wot-button size="small" @click="clearRecent">清除</wot-button>
        </view>
        <view class="book-grid">
          <BookCard 
            v-for="book in recentBooks" 
            :key="book.bookId"
            :book="book"
            @click="readBook(book)"
          />
        </view>
      </view>
      
      <!-- 推荐合集 -->
      <view class="section">
        <view class="section-title">推荐合集</view>
        <view class="collection-list">
          <view 
            class="collection-item"
            v-for="collection in collections"
            :key="collection.setId"
            @click="goToCollection(collection)"
          >
            <image :src="collection.coverUrl" class="collection-cover" />
            <view class="collection-info">
              <text class="collection-name">{{ collection.title }}</text>
              <text class="collection-count">{{ collection.bookCount }}本</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 目录抽屉 -->
    <wot-drawer v-model="showDrawer" position="left">
      <view class="drawer-content">
        <view class="category-tree">
          <view 
            class="category-item"
            v-for="category in categories"
            :key="category.categoryId"
            @click="selectCategory(category)"
          >
            {{ category.name }}
          </view>
        </view>
      </view>
    </wot-drawer>
  </view>
</template>
```

#### 3.2.2 班级页面

```vue
<!-- pages/class/index/index.vue -->
<template>
  <view class="class-container">
    <!-- 未加入班级状态 -->
    <view v-if="!currentClass" class="no-class">
      <wot-button @click="showJoinModal = true">加入班级</wot-button>
      <wot-button @click="showCreateModal = true">创建班级</wot-button>
    </view>
    
    <!-- 已加入班级状态 -->
    <view v-else class="class-content">
      <!-- 班级信息头部 -->
      <view class="class-header">
        <view class="class-info">
          <text class="class-name">{{ currentClass.className }}</text>
          <text class="member-count">{{ currentClass.userCount }}人</text>
        </view>
        <view class="class-actions">
          <wot-button size="small" @click="switchClass">切换班级</wot-button>
          <wot-button size="small" @click="showInviteModal = true">邀请成员</wot-button>
          <wot-button 
            v-if="isTeacher" 
            size="small" 
            @click="goToManagement"
          >
            班级管理
          </wot-button>
        </view>
        <view class="class-code">
          班级口令: {{ currentClass.classCode }}
        </view>
      </view>
      
      <!-- Tab栏 -->
      <wot-tabs v-model="activeTab">
        <wot-tab title="作业" name="assignment" />
        <wot-tab :title="`打卡 (${checkinCount}/${totalMembers})`" name="checkin" />
        <wot-tab title="成员" name="member" />
      </wot-tabs>
      
      <!-- 作业页签 -->
      <view v-if="activeTab === 'assignment'" class="tab-content">
        <wot-button 
          v-if="isTeacher" 
          @click="createAssignment"
          class="create-btn"
        >
          创建作业
        </wot-button>
        
        <view class="assignment-list">
          <AssignmentCard
            v-for="assignment in assignments"
            :key="assignment.workId"
            :assignment="assignment"
            :user-role="userRole"
            @click="goToAssignmentDetail(assignment)"
          />
        </view>
      </view>
      
      <!-- 打卡页签 -->
      <view v-if="activeTab === 'checkin'" class="tab-content">
        <view class="checkin-list">
          <CheckinCard
            v-for="checkin in checkinList"
            :key="checkin.punchId"
            :checkin="checkin"
          />
        </view>
      </view>
      
      <!-- 成员页签 -->
      <view v-if="activeTab === 'member'" class="tab-content">
        <view class="member-list">
          <view 
            class="member-item"
            v-for="member in members"
            :key="member.userId"
          >
            <UserAvatar :user="member" />
            <view class="member-info">
              <text class="member-name">{{ member.nickname }}</text>
              <text class="member-stats">总阅读{{ member.totalDays }}天·{{ member.totalBooks }}本</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
```

## 4. 后端架构设计

### 4.1 数据库设计

#### 4.1.1 核心数据表

```sql
-- 用户表
CREATE TABLE `user` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `open_id` varchar(64) NOT NULL COMMENT '微信OpenID',
  `union_id` varchar(64) DEFAULT NULL COMMENT '微信UnionID', 
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar_url` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `vip_status` tinyint DEFAULT 0 COMMENT 'VIP状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_open_id` (`open_id`)
);

-- 绘本表
CREATE TABLE `book` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL COMMENT '绘本标题',
  `cover_url` varchar(255) DEFAULT NULL COMMENT '封面URL',
  `language` varchar(10) NOT NULL COMMENT '语言(en/zh)',
  `level` varchar(20) DEFAULT NULL COMMENT '分级(AA-H)',
  `category_id` bigint DEFAULT NULL COMMENT '分类ID',
  `book_set_id` bigint DEFAULT NULL COMMENT '绘本集ID',
  `audio_url` varchar(255) DEFAULT NULL COMMENT '音频URL',
  `content_url` varchar(255) DEFAULT NULL COMMENT '内容URL',
  `page_count` int DEFAULT 0 COMMENT '页数',
  `status` tinyint DEFAULT 1 COMMENT '状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category_id`),
  KEY `idx_book_set` (`book_set_id`)
);

-- 班级表
CREATE TABLE `class` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `class_name` varchar(50) NOT NULL COMMENT '班级名称',
  `class_code` varchar(20) NOT NULL COMMENT '班级口令',
  `creator_id` bigint NOT NULL COMMENT '创建者ID',
  `member_count` int DEFAULT 0 COMMENT '成员数量',
  `status` tinyint DEFAULT 1 COMMENT '状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_class_code` (`class_code`),
  KEY `idx_creator` (`creator_id`)
);

-- 班级成员表
CREATE TABLE `class_member` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `class_id` bigint NOT NULL COMMENT '班级ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role` varchar(20) DEFAULT 'student' COMMENT '角色(teacher/student)',
  `join_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_class_user` (`class_id`, `user_id`),
  KEY `idx_user` (`user_id`)
);

-- 作业表
CREATE TABLE `assignment` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `class_id` bigint NOT NULL COMMENT '班级ID',
  `title` varchar(100) NOT NULL COMMENT '作业标题',
  `description` text COMMENT '作业描述',
  `book_id` bigint DEFAULT NULL COMMENT '关联绘本ID',
  `assignment_type` varchar(20) NOT NULL COMMENT '作业类型(reading/dubbing)',
  `due_date` datetime DEFAULT NULL COMMENT '截止时间',
  `creator_id` bigint NOT NULL COMMENT '创建者ID',
  `status` tinyint DEFAULT 1 COMMENT '状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_class` (`class_id`),
  KEY `idx_creator` (`creator_id`)
);

-- 用户阅读记录表
CREATE TABLE `reading_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `book_id` bigint NOT NULL COMMENT '绘本ID',
  `read_date` date NOT NULL COMMENT '阅读日期',
  `read_duration` int DEFAULT 0 COMMENT '阅读时长(秒)',
  `progress` decimal(5,2) DEFAULT 0.00 COMMENT '阅读进度',
  `is_completed` tinyint DEFAULT 0 COMMENT '是否完成',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_book_date` (`user_id`, `book_id`, `read_date`),
  KEY `idx_user_date` (`user_id`, `read_date`)
);

-- 打卡记录表
CREATE TABLE `punch_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `punch_date` date NOT NULL COMMENT '打卡日期',
  `book_count` int DEFAULT 0 COMMENT '当日阅读本数',
  `punch_text` varchar(500) DEFAULT NULL COMMENT '打卡文字',
  `images` json DEFAULT NULL COMMENT '打卡图片',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_date` (`user_id`, `punch_date`),
  KEY `idx_punch_date` (`punch_date`)
);
```

### 4.2 API接口设计

#### 4.2.1 Controller层设计

```java
// 班级管理控制器
@RestController
@RequestMapping("/api/class")
public class ClassController {
    
    @GetMapping("/list")
    public Result<List<ClassVO>> getClassList() {
        // 获取用户班级列表
    }
    
    @GetMapping("/info")
    public Result<ClassDetailVO> getClassInfo(@RequestParam Long classId) {
        // 获取班级详情
    }
    
    @PostMapping("/join")
    public Result<Void> joinClass(@RequestBody JoinClassDTO dto) {
        // 加入班级
    }
    
    @PostMapping("/create")
    public Result<ClassVO> createClass(@RequestBody CreateClassDTO dto) {
        // 创建班级
    }
}

// 作业管理控制器
@RestController
@RequestMapping("/api/classwork")
public class AssignmentController {
    
    @GetMapping("/list")
    public Result<List<AssignmentVO>> getAssignmentList(@RequestParam Long classId) {
        // 获取班级作业列表
    }
    
    @GetMapping("/progress")
    public Result<AssignmentProgressVO> getAssignmentProgress(
        @RequestParam Long classId, 
        @RequestParam Long workId
    ) {
        // 获取作业完成进度
    }
    
    @GetMapping("/completed")
    public Result<AssignmentMemberVO> getCompletedMembers(@RequestParam Long workId) {
        // 获取已完成作业的成员
    }
    
    @GetMapping("/uncompleted") 
    public Result<AssignmentMemberVO> getUncompletedMembers(@RequestParam Long workId) {
        // 获取未完成作业的成员
    }
}
```

#### 4.2.2 Service层设计

```java
@Service
public class ClassService {
    
    @Autowired
    private ClassMapper classMapper;
    
    @Autowired
    private ClassMemberMapper classMemberMapper;
    
    /**
     * 获取用户班级列表
     */
    public List<ClassVO> getUserClassList(Long userId) {
        List<ClassMember> memberList = classMemberMapper.selectByUserId(userId);
        List<Long> classIds = memberList.stream()
            .map(ClassMember::getClassId)
            .collect(Collectors.toList());
            
        if (CollectionUtils.isEmpty(classIds)) {
            return Collections.emptyList();
        }
        
        List<Class> classList = classMapper.selectByIds(classIds);
        return classList.stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
    }
    
    /**
     * 创建班级
     */
    @Transactional
    public ClassVO createClass(CreateClassDTO dto, Long userId) {
        // 生成唯一班级口令
        String classCode = generateClassCode();
        
        // 创建班级
        Class clazz = new Class();
        clazz.setClassName(dto.getClassName());
        clazz.setClassCode(classCode);
        clazz.setCreatorId(userId);
        clazz.setMemberCount(1);
        classMapper.insert(clazz);
        
        // 添加创建者为班级成员
        ClassMember member = new ClassMember();
        member.setClassId(clazz.getId());
        member.setUserId(userId);
        member.setRole("teacher");
        classMemberMapper.insert(member);
        
        return convertToVO(clazz);
    }
    
    private String generateClassCode() {
        // 生成6位随机班级口令
        return RandomStringUtils.randomAlphanumeric(6).toUpperCase();
    }
}
```

## 5. 数据流设计

### 5.1 用户认证流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant MP as 小程序
    participant WX as 微信服务器
    participant API as 后端API
    
    U->>MP: 点击登录
    MP->>WX: wx.login()
    WX-->>MP: 返回code
    MP->>API: 发送code到后端
    API->>WX: 使用code换取openid
    WX-->>API: 返回openid
    API->>API: 查询/创建用户
    API-->>MP: 返回token和用户信息
    MP->>MP: 存储token
    MP-->>U: 登录成功
```

### 5.2 绘本阅读流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant MP as 小程序
    participant API as 后端API
    participant OSS as 文件存储
    
    U->>MP: 选择绘本
    MP->>API: 获取绘本详情
    API-->>MP: 返回绘本信息
    MP->>OSS: 加载绘本内容
    OSS-->>MP: 返回图片/音频
    MP-->>U: 展示绘本内容
    U->>MP: 开始阅读
    MP->>MP: 记录阅读进度
    U->>MP: 完成阅读
    MP->>API: 提交阅读记录
    API-->>MP: 记录成功
```

### 5.3 打卡流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant MP as 小程序
    participant API as 后端API
    
    U->>MP: 点击打卡
    MP->>API: 检查当日阅读量
    API-->>MP: 返回阅读统计
    alt 阅读量 > 0
        MP-->>U: 显示打卡界面
        U->>MP: 填写打卡内容
        MP->>API: 提交打卡记录
        API-->>MP: 打卡成功
        MP-->>U: 显示打卡成功
    else 阅读量 = 0
        MP-->>U: 提示需要先阅读
    end
```

## 6. 错误处理与异常设计

### 6.1 前端错误处理

```typescript
// utils/request.ts
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

class RequestError extends Error {
  code: number;
  
  constructor(code: number, message: string) {
    super(message);
    this.code = code;
  }
}

export const request = <T = any>(options: RequestOptions): Promise<T> => {
  return new Promise((resolve, reject) => {
    uni.request({
      ...options,
      success: (res) => {
        const response = res.data as ApiResponse<T>;
        
        if (response.code === 200) {
          resolve(response.data);
        } else {
          // 统一错误处理
          handleApiError(response.code, response.message);
          reject(new RequestError(response.code, response.message));
        }
      },
      fail: (err) => {
        // 网络错误处理
        uni.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
        reject(new RequestError(-1, '网络请求失败'));
      }
    });
  });
};

const handleApiError = (code: number, message: string) => {
  switch (code) {
    case 401:
      // 未登录，跳转登录页
      uni.navigateTo({ url: '/pages/login/index' });
      break;
    case 403:
      // 无权限
      uni.showToast({ title: '无权限访问', icon: 'none' });
      break;
    case 500:
      // 服务器错误
      uni.showToast({ title: '服务器错误', icon: 'none' });
      break;
    default:
      // 其他错误
      uni.showToast({ title: message || '请求失败', icon: 'none' });
  }
};
```

### 6.2 后端异常处理

```java
// 全局异常处理器
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(BusinessException.class)
    public Result<Void> handleBusinessException(BusinessException e) {
        log.warn("业务异常: {}", e.getMessage());
        return Result.error(e.getCode(), e.getMessage());
    }
    
    @ExceptionHandler(ValidationException.class)
    public Result<Void> handleValidationException(ValidationException e) {
        log.warn("参数校验异常: {}", e.getMessage());
        return Result.error(400, "参数校验失败: " + e.getMessage());
    }
    
    @ExceptionHandler(Exception.class)
    public Result<Void> handleException(Exception e) {
        log.error("系统异常", e);
        return Result.error(500, "系统异常，请稍后重试");
    }
}

// 业务异常类
public class BusinessException extends RuntimeException {
    private final int code;
    
    public BusinessException(int code, String message) {
        super(message);
        this.code = code;
    }
    
    public BusinessException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.code = errorCode.getCode();
    }
}

// 错误码枚举
public enum ErrorCode {
    USER_NOT_FOUND(1001, "用户不存在"),
    CLASS_NOT_FOUND(2001, "班级不存在"),
    CLASS_CODE_INVALID(2002, "班级口令无效"),
    ASSIGNMENT_NOT_FOUND(3001, "作业不存在"),
    PERMISSION_DENIED(4001, "权限不足"),
    BOOK_NOT_FOUND(5001, "绘本不存在");
    
    private final int code;
    private final String message;
    
    ErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }
}
```

## 7. 性能优化设计

### 7.1 前端性能优化

#### 7.1.1 图片懒加载

```typescript
// components/LazyImage/LazyImage.vue
<template>
  <view class="lazy-image-container">
    <image 
      v-if="shouldLoad"
      :src="src"
      :class="imageClass"
      @load="onLoad"
      @error="onError"
    />
    <view v-else class="placeholder">
      <wot-loading />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

interface Props {
  src: string;
  threshold?: number;
}

const props = withDefaults(defineProps<Props>(), {
  threshold: 100
});

const shouldLoad = ref(false);

onMounted(() => {
  // 使用 Intersection Observer 实现懒加载
  const observer = uni.createIntersectionObserver();
  observer.relativeToViewport({ bottom: props.threshold });
  observer.observe('.lazy-image-container', (res) => {
    if (res.intersectionRatio > 0) {
      shouldLoad.value = true;
      observer.disconnect();
    }
  });
});
</script>
```

#### 7.1.2 列表虚拟滚动

```typescript
// components/VirtualList/VirtualList.vue
<template>
  <scroll-view 
    class="virtual-list"
    scroll-y
    :scroll-top="scrollTop"
    @scroll="onScroll"
  >
    <view :style="{ height: totalHeight + 'px' }">
      <view 
        class="virtual-item"
        v-for="item in visibleItems"
        :key="item.id"
        :style="{ 
          transform: `translateY(${item.top}px)`,
          position: 'absolute',
          width: '100%'
        }"
      >
        <slot :item="item.data" />
      </view>
    </view>
  </scroll-view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

interface Props {
  items: any[];
  itemHeight: number;
  containerHeight: number;
}

const props = defineProps<Props>();

const scrollTop = ref(0);
const visibleCount = computed(() => Math.ceil(props.containerHeight / props.itemHeight) + 2);
const startIndex = computed(() => Math.floor(scrollTop.value / props.itemHeight));
const endIndex = computed(() => Math.min(startIndex.value + visibleCount.value, props.items.length));

const visibleItems = computed(() => {
  const items = [];
  for (let i = startIndex.value; i < endIndex.value; i++) {
    items.push({
      id: i,
      data: props.items[i],
      top: i * props.itemHeight
    });
  }
  return items;
});

const totalHeight = computed(() => props.items.length * props.itemHeight);

const onScroll = (e: any) => {
  scrollTop.value = e.detail.scrollTop;
};
</script>
```

### 7.2 后端性能优化

#### 7.2.1 Redis缓存设计

```java
@Service
public class BookService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String BOOK_CACHE_KEY = "book:detail:";
    private static final String BOOK_LIST_CACHE_KEY = "book:list:";
    private static final long CACHE_EXPIRE_TIME = 3600; // 1小时
    
    /**
     * 获取绘本详情（带缓存）
     */
    public BookDetailVO getBookDetail(Long bookId) {
        String cacheKey = BOOK_CACHE_KEY + bookId;
        
        // 先从缓存获取
        BookDetailVO cached = (BookDetailVO) redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        // 缓存未命中，查询数据库
        Book book = bookMapper.selectById(bookId);
        if (book == null) {
            throw new BusinessException(ErrorCode.BOOK_NOT_FOUND);
        }
        
        BookDetailVO result = convertToDetailVO(book);
        
        // 写入缓存
        redisTemplate.opsForValue().set(cacheKey, result, CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
        
        return result;
    }
    
    /**
     * 获取绘本列表（带缓存）
     */
    public List<BookVO> getBookList(Long categoryId, int page, int size) {
        String cacheKey = BOOK_LIST_CACHE_KEY + categoryId + ":" + page + ":" + size;
        
        List<BookVO> cached = (List<BookVO>) redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        // 查询数据库
        PageHelper.startPage(page, size);
        List<Book> books = bookMapper.selectByCategoryId(categoryId);
        List<BookVO> result = books.stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
        
        // 写入缓存（较短过期时间，因为列表可能变化）
        redisTemplate.opsForValue().set(cacheKey, result, 600, TimeUnit.SECONDS);
        
        return result;
    }
}
```

#### 7.2.2 数据库查询优化

```java
// 使用MyBatis-Plus的批量查询优化
@Service
public class ReadingRecordService {
    
    /**
     * 批量获取用户阅读状态
     */
    public Map<Long, ReadingStatus> getBatchReadingStatus(Long userId, List<Long> bookIds) {
        if (CollectionUtils.isEmpty(bookIds)) {
            return Collections.emptyMap();
        }
        
        // 使用IN查询，一次性获取所有状态
        List<ReadingRecord> records = readingRecordMapper.selectList(
            Wrappers.<ReadingRecord>lambdaQuery()
                .eq(ReadingRecord::getUserId, userId)
                .in(ReadingRecord::getBookId, bookIds)
        );
        
        // 转换为Map便于查找
        return records.stream()
            .collect(Collectors.toMap(
                ReadingRecord::getBookId,
                record -> ReadingStatus.builder()
                    .isRead(record.getIsCompleted() == 1)
                    .progress(record.getProgress())
                    .readDate(record.getReadDate())
                    .build()
            ));
    }
}

// 数据库索引优化
/*
-- 为常用查询添加复合索引
CREATE INDEX idx_reading_record_user_date ON reading_record(user_id, read_date);
CREATE INDEX idx_class_member_class_role ON class_member(class_id, role);
CREATE INDEX idx_assignment_class_status ON assignment(class_id, status);
CREATE INDEX idx_punch_record_date ON punch_record(punch_date DESC);

-- 为分页查询优化
CREATE INDEX idx_book_category_status ON book(category_id, status, create_time DESC);
*/
```

## 8. 测试策略

### 8.1 前端测试

```typescript
// 组件单元测试示例
// tests/components/BookCard.test.ts
import { mount } from '@vue/test-utils';
import BookCard from '@/components/BookCard/BookCard.vue';

describe('BookCard', () => {
  const mockBook = {
    bookId: '1',
    title: '测试绘本',
    coverUrl: 'https://example.com/cover.jpg',
    readStatus: 'unread'
  };

  it('应该正确渲染绘本信息', () => {
    const wrapper = mount(BookCard, {
      props: { book: mockBook }
    });

    expect(wrapper.find('.book-title').text()).toBe('测试绘本');
    expect(wrapper.find('.book-cover').attributes('src')).toBe(mockBook.coverUrl);
  });

  it('点击卡片应该触发click事件', async () => {
    const wrapper = mount(BookCard, {
      props: { book: mockBook }
    });

    await wrapper.trigger('click');
    expect(wrapper.emitted('click')).toBeTruthy();
    expect(wrapper.emitted('click')[0]).toEqual([mockBook]);
  });
});
```

### 8.2 后端测试

```java
// 服务层单元测试
@SpringBootTest
class ClassServiceTest {
    
    @Autowired
    private ClassService classService;
    
    @MockBean
    private ClassMapper classMapper;
    
    @MockBean
    private ClassMemberMapper classMemberMapper;
    
    @Test
    void testCreateClass() {
        // Given
        CreateClassDTO dto = new CreateClassDTO();
        dto.setClassName("测试班级");
        Long userId = 1L;
        
        // When
        ClassVO result = classService.createClass(dto, userId);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getClassName()).isEqualTo("测试班级");
        assertThat(result.getClassCode()).hasSize(6);
        
        // 验证数据库操作
        verify(classMapper).insert(any(Class.class));
        verify(classMemberMapper).insert(any(ClassMember.class));
    }
    
    @Test
    void testJoinClassWithInvalidCode() {
        // Given
        String invalidCode = "INVALID";
        when(classMapper.selectByClassCode(invalidCode)).thenReturn(null);
        
        // When & Then
        assertThatThrownBy(() -> classService.joinClass(invalidCode, 1L))
            .isInstanceOf(BusinessException.class)
            .hasMessage("班级口令无效");
    }
}

// 集成测试
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
class ClassControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void testGetClassList() {
        // Given
        String url = "/api/class/list";
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Bearer " + getTestToken());
        
        // When
        ResponseEntity<Result> response = restTemplate.exchange(
            url, HttpMethod.GET, new HttpEntity<>(headers), Result.class
        );
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().getCode()).isEqualTo(200);
    }
}
```

这份设计文档涵盖了绘本阅读小程序的完整技术架构，包括前端组件设计、后端服务架构、数据库设计、API接口、性能优化和测试策略等各个方面。文档基于requirements.md和api.md的内容，提供了详细的技术实现方案。