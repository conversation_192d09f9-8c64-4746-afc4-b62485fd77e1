### **一、 班级管理 (Class Management)**

这部分接口用于处理班级创建、加入、信息查询和作业管理等功能。

| 方法 | 接口路径 | 描述 | 请求参数 | 返回字段 |
| :--- | :--- | :--- | :--- | :--- |
| GET | `/api/class/list` | 查询当前用户已加入的所有班级。 | `userId` (隐式) | 班级对象列表，用于判断用户是否已加入班级。 |
| GET | `/api/class/info` | 查询指定班级的详细信息。 | `classId` | `className` (班级名), `userCount` (人数), `classCode` (口令)等。 |
| GET | `/api/classwork/list` | 查询指定班级的作业列表。 | `classId` | 作业卡片对象列表。 |
| GET | `/api/classwork/progress` | 查询作业的整体完成进度。 | `classId`, `workId` | `completedCount` (已完成人数), `totalCount` (总人数)。 |
| GET | `/api/classwork/completed` | 获取指定作业的已完成成员列表。 | `workId` | `workId`, `completedUsers`: [ { `userId`, `nickname`, `avatar`, `status` (完成状态，如“已配音”), `recordUrl` (录音地址) } ] |
| GET | `/api/classwork/uncompleted` | 获取指定作业的未完成成员列表。 | `workId` | `workId`, `uncompletedUsers`: [ { `userId`, `nickname`, `avatar` } ] |

---

### **二、 用户认证与信息 (Authentication & User Profile)**

这部分接口负责用户认证、资料的获取、更新以及个人中心的数据展示。

| 方法 | 接口路径 | 描述 | 请求体/参数 | 返回字段 |
| :--- | :--- | :--- | :--- | :--- |
| **认证相关** | | | | |
| POST | `/api/auth/login` | 微信授权登录，获取用户token和基本信息。 | (Body) `code` (微信登录code) | `token`, `userInfo`: { `userId`, `openId`, `nickname`, `avatarUrl`, `vipStatus` } |
| POST | `/api/auth/refresh` | 刷新用户token。 | (Header) `Authorization` | `token`, `expiresIn` |
| POST | `/api/auth/logout` | 用户退出登录。 | (Header) `Authorization` | 操作成功或失败的状态。 |
| **用户信息** | | | | |
| GET | `/api/user/info` | 获取用户的公开基本信息。 | `userId` | `userId`, `nickname`, `avatarUrl`, `phone` |
| POST | `/api/user/update` | 更新并保存用户的个人资料。 | (Body) `nickname`, `avatarUrl`, `phone` | 保存成功或失败的状态。 |
| GET | `/api/user/me` | 获取当前登录用户的个人中心数据。 | `userId` (隐式) | `userId`, `nickname`, `avatarUrl`, `vipStatus` (会员状态) |
| GET | `/api/userStats` | 获取用户的阅读统计数据。 | `userId` | `totalDays`, `totalBooks`, `todayBooks`, `weekBooks`, `monthBooks` |

---

### **三、 书架与绘本 (Bookshelf & Books)**

这部分接口用于展示中英文书架、绘本集、分类以及用户的阅读计划。

| 方法 | 接口路径 | 描述 | 请求参数 | 返回字段 |
| :--- | :--- | :--- | :--- | :--- |
| GET | `/api/bookCategories` | 获取绘本的分类目录。 | `lang` (如 'en') | `categoryId`, `name`, `type` 的对象列表。 |
| GET | `/api/bookList` | 根据分类获取绘本列表。 | `categoryId` | `bookId`, `title`, `coverUrl`, `source`, `totalCount` 的对象列表。 |
| GET | `/api/bookSet/detail` | 获取绘本集详情。 | `setId` | `setName` (合集名称), `bookList`: [ { `bookId`, `title`, `coverUrl`, `readCount`, `status` } ] |
| GET | `/api/bookshelf/cn` | 获取中文书架的合集列表。 | `userId` | `title`, `coverUrl`, `bookCount`, `bookSetId` 的对象列表。 |
| GET | `/api/recommendTopics` | 获取推荐主题内容（如小猪佩奇）。 | `type` (如 'peppa') | 各季合集书名、本数、封面、合集ID等。 |
| GET | `/api/readingPlans` | 查询用户的阅读计划列表。 | `userId` | 阅读计划对象列表。 |
| GET | `/api/readingPlans/{planId}` | 获取特定阅读计划的详情。 | `planId` (路径参数) | `planId`, `planName`, `coverUrl`, `currentDay`, `totalDays`, `bookList` |

---

### **四、 用户行为与记录 (User Actions & Records)**

这部分接口处理用户的打卡、阅读、收藏、配音等行为记录。

| 方法 | 接口路径 | 描述 | 请求参数 | 返回字段 |
| :--- | :--- | :--- | :--- | :--- |
| **阅读记录** | | | | |
| GET | `/api/recentBooks` | 获取最近阅读的绘本。 | `userId` | 绘本对象列表（最多3条）。 |
| DELETE | `/api/recentBooks` | 清空最近阅读记录。 | `userId` | 操作成功或失败的状态。 |
| GET | `/api/bookshelf/cn/recent` | 获取最近阅读的中文绘本。 | `userId` | 中文绘本对象列表。 |
| DELETE | `/api/bookshelf/cn/recent` | 清空最近阅读的中文绘本记录。 | `userId` | 操作成功或失败的状态。 |
| GET | `/api/readStatus` | 批量查询绘本的阅读状态。 | `userId`, `bookIdList` | 各绘本ID对应的阅读状态列表。 |
| GET | `/api/readingHistory` (推断) | 获取完整的用户阅读历史记录。 | `userId` | `todayCount`, `weekCount`, `readingDays`, `joinDays`, `records`: [ { `id`, `bookTitle`, `coverUrl`, `readAt`, `status` } ] |
| **打卡 (Punch-in)** | | | | |
| GET | `/api/punch/list` | 获取打卡记录列表。 | `tab` ('latest', 'follow', 'mine') | `punchId`, `userId`, `nickname`, `avatar`, `punchText`, `timestamp`, `dayCount`, `bookList[]` 的对象列表。 |
| GET | `/api/punch/calendar` | 获取指定月份的打卡日历数据。 | `userId` | `date`, `hasPunched`, `readCount` 的对象列表。 |
| GET | `/api/punch/calendar/all` | 获取所有月份的打卡日历数据。 | `userId` | `month`, `punchDates[]`, `count` 的对象列表。 |
| **收藏 (Collections)** | | | | |
| GET | `/api/collections` (推断) | 获取用户的收藏列表（绘本和绘本集）。 | `userId` (隐式) | `collections`: [ { `type`, `id`, `title`, `coverUrl`, `readStatus` (绘本) / `count` (绘本集) } ] |
| DELETE | `/api/collections/{type}/{id}` (推断) | 删除一个收藏项。 | `type`, `id` | 操作成功或失败的状态。 |
| **配音与音频 (Recording & Audio)** | | | | |
| GET | `/api/recordings/list` | 获取用户的个人配音作品列表。 | `userId`, `page`, `pageSize` | `bookTitle`, `coverUrl`, `score`, `recordTime`, `recordId`, `isShareable` 的对象列表。 |
| GET | `/api/audioList` | 获取“磨耳朵”的音频列表。 | `type` ('recent', 'en', 'zh', 'fav') | `audioId`, `title`, `coverUrl`, `source`, `duration` 的对象列表。 |
| DELETE | `/api/audioItem/{audioId}` | 从“最近”或“收藏”中移除一个音频。 | `audioId` (路径参数) | 操作成功或失败的状态。 |