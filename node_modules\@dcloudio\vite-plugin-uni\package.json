{"name": "@dcloudio/vite-plugin-uni", "version": "3.0.0-alpha-3000020210521001", "description": "uni-app vite plugin", "bin": {"uni": "bin/uni.js"}, "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*.js", "dist/**/*.d.ts"], "buildOptions": {"bundler": "tsc"}, "repository": {"type": "git", "url": "git+https://github.com/dcloudio/uni-app.git", "directory": "packages/vite-plugin-uni"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "license": "Apache-2.0", "dependencies": {"@rollup/pluginutils": "^4.1.0", "autoprefixer": "^10.2.5", "cac": "^6.7.3", "chalk": "^4.1.1", "debug": "^4.3.1", "estree-walker": "^2.0.1", "express": "^4.17.1", "fs-extra": "^9.0.1", "jsonc-parser": "^3.0.0", "mime": "^2.5.2", "module-alias": "^2.2.2", "postcss-selector-parser": "^6.0.4", "rollup-plugin-copy": "^3.4.0", "slash": "^3.0.0"}, "peerDependencies": {"@vitejs/plugin-vue": "^1.2.2", "@vue/compiler-sfc": "^3.1.0-beta.3", "@vue/server-renderer": "^3.1.0-beta.3", "@vue/shared": "^3.1.0-beta.3", "vite": "^2.3.0"}, "devDependencies": {"@types/express": "^4.17.11", "@types/mime": "^2.0.3", "@types/module-alias": "^2.0.0", "@types/sass": "^1.16.0"}, "uni-app": {"compilerVersion": "3.1.2"}}