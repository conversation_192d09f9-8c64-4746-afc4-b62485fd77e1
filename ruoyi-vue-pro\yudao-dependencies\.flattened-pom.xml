<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>cn.iocoder.boot</groupId>
  <artifactId>yudao-dependencies</artifactId>
  <version>2025.08-jdk8-SNAPSHOT</version>
  <packaging>pom</packaging>
  <name>yudao-dependencies</name>
  <description>基础 bom 文件，管理整个项目的依赖版本</description>
  <url>https://github.com/YunaiV/ruoyi-vue-pro</url>
  <properties>
    <logback.version>1.2.13</logback.version>
    <mybatis-plus-join.version>1.5.4</mybatis-plus-join.version>
    <spring.framework.version>5.3.39</spring.framework.version>
    <podam.version>7.2.11.RELEASE</podam.version>
    <velocity.version>2.4</velocity.version>
    <spring.boot.version>2.7.18</spring.boot.version>
    <mqtt.version>1.2.5</mqtt.version>
    <tika-core.version>2.9.3</tika-core.version>
    <lock4j.version>2.2.7</lock4j.version>
    <awssdk.version>2.30.14</awssdk.version>
    <lombok.version>1.18.38</lombok.version>
    <jsoup.version>1.18.3</jsoup.version>
    <fastjson.version>1.2.83</fastjson.version>
    <commons-net.version>3.11.1</commons-net.version>
    <kingbase.jdbc.version>8.6.0</kingbase.jdbc.version>
    <jimubi.version>1.9.5</jimubi.version>
    <netty.version>4.1.118.Final</netty.version>
    <opengauss.jdbc.version>5.1.0</opengauss.jdbc.version>
    <taos.version>3.3.3</taos.version>
    <hutool.version>5.8.35</hutool.version>
    <transmittable-thread-local.version>2.14.5</transmittable-thread-local.version>
    <flowable.version>6.8.0</flowable.version>
    <redisson.version>3.41.0</redisson.version>
    <easy-trans.version>3.0.6</easy-trans.version>
    <bizlog-sdk.version>3.0.6</bizlog-sdk.version>
    <mybatis-plus.version>3.5.12</mybatis-plus.version>
    <druid.version>1.2.24</druid.version>
    <pf4j-spring.version>0.9.0</pf4j-spring.version>
    <springdoc.version>1.8.0</springdoc.version>
    <justauth-starter.version>1.4.0</justauth-starter.version>
    <mockito-inline.version>4.11.0</mockito-inline.version>
    <vertx.version>4.5.13</vertx.version>
    <jedis-mock.version>1.1.8</jedis-mock.version>
    <dm8.jdbc.version>*********</dm8.jdbc.version>
    <rocketmq-spring.version>2.3.2</rocketmq-spring.version>
    <ip2region.version>2.7.0</ip2region.version>
    <jsch.version>0.1.55</jsch.version>
    <fastexcel.version>1.2.0</fastexcel.version>
    <weixin-java.version>4.7.5.B</weixin-java.version>
    <servlet.versoin>2.5</servlet.versoin>
    <dynamic-datasource.version>4.3.1</dynamic-datasource.version>
    <knife4j.version>4.5.0</knife4j.version>
    <spring-boot-admin.version>2.7.15</spring-boot-admin.version>
    <mapstruct.version>1.6.3</mapstruct.version>
    <mybatis.version>3.5.19</mybatis.version>
    <jimureport.version>2.1.0</jimureport.version>
    <justauth.version>1.16.7</justauth.version>
    <guava.version>33.4.8-jre</guava.version>
    <anji-plus-captcha.version>1.4.0</anji-plus-captcha.version>
    <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>
    <opentracing.version>0.33.0</opentracing.version>
    <skywalking.version>8.12.0</skywalking.version>
    <spring.security.version>5.8.16</spring.security.version>
    <revision>2025.08-jdk8-SNAPSHOT</revision>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-bom</artifactId>
        <version>${netty.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-framework-bom</artifactId>
        <version>${spring.framework.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-bom</artifactId>
        <version>${spring.security.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring.boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.github.mouzt</groupId>
        <artifactId>bizlog-sdk</artifactId>
        <version>${bizlog-sdk.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-biz-tenant</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-biz-data-permission</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-biz-ip</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-configuration-processor</artifactId>
        <version>${spring.boot.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-web</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-security</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-websocket</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.github.xiaoymin</groupId>
        <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
        <version>${knife4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springdoc</groupId>
        <artifactId>springdoc-openapi-ui</artifactId>
        <version>${springdoc.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-mybatis</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid-spring-boot-starter</artifactId>
        <version>${druid.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mybatis</groupId>
        <artifactId>mybatis</artifactId>
        <version>${mybatis.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>${mybatis-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-jsqlparser-4.9</artifactId>
        <version>${mybatis-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-generator</artifactId>
        <version>${mybatis-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        <version>${dynamic-datasource.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.yulichang</groupId>
        <artifactId>mybatis-plus-join-boot-starter</artifactId>
        <version>${mybatis-plus-join.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fhs-opensource</groupId>
        <artifactId>easy-trans-spring-boot-starter</artifactId>
        <version>${easy-trans.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-commons</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.fhs-opensource</groupId>
        <artifactId>easy-trans-mybatis-plus-extend</artifactId>
        <version>${easy-trans.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fhs-opensource</groupId>
        <artifactId>easy-trans-anno</artifactId>
        <version>${easy-trans.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-redis</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>org.redisson</groupId>
        <artifactId>redisson-spring-boot-starter</artifactId>
        <version>${redisson.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-data-34</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.redisson</groupId>
        <artifactId>redisson-spring-data-27</artifactId>
        <version>${redisson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.dameng</groupId>
        <artifactId>DmJdbcDriver18</artifactId>
        <version>${dm8.jdbc.version}</version>
      </dependency>
      <dependency>
        <groupId>org.opengauss</groupId>
        <artifactId>opengauss-jdbc</artifactId>
        <version>${opengauss.jdbc.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.com.kingbase</groupId>
        <artifactId>kingbase8</artifactId>
        <version>${kingbase.jdbc.version}</version>
      </dependency>
      <dependency>
        <groupId>com.taosdata.jdbc</groupId>
        <artifactId>taos-jdbcdriver</artifactId>
        <version>${taos.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-job</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-mq</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.rocketmq</groupId>
        <artifactId>rocketmq-spring-boot-starter</artifactId>
        <version>${rocketmq-spring.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-protection</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
        <version>${lock4j.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-monitor</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.skywalking</groupId>
        <artifactId>apm-toolkit-trace</artifactId>
        <version>${skywalking.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.skywalking</groupId>
        <artifactId>apm-toolkit-logback-1.x</artifactId>
        <version>${skywalking.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.skywalking</groupId>
        <artifactId>apm-toolkit-opentracing</artifactId>
        <version>${skywalking.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opentracing</groupId>
        <artifactId>opentracing-api</artifactId>
        <version>${opentracing.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opentracing</groupId>
        <artifactId>opentracing-util</artifactId>
        <version>${opentracing.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opentracing</groupId>
        <artifactId>opentracing-noop</artifactId>
        <version>${opentracing.version}</version>
      </dependency>
      <dependency>
        <groupId>de.codecentric</groupId>
        <artifactId>spring-boot-admin-starter-server</artifactId>
        <version>${spring-boot-admin.version}</version>
        <exclusions>
          <exclusion>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-server-cloud</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>de.codecentric</groupId>
        <artifactId>spring-boot-admin-starter-client</artifactId>
        <version>${spring-boot-admin.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-test</artifactId>
        <version>${revision}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-inline</artifactId>
        <version>${mockito-inline.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <version>${spring.boot.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.github.fppt</groupId>
        <artifactId>jedis-mock</artifactId>
        <version>${jedis-mock.version}</version>
      </dependency>
      <dependency>
        <groupId>uk.co.jemos.podam</groupId>
        <artifactId>podam</artifactId>
        <version>${podam.version}</version>
      </dependency>
      <dependency>
        <groupId>org.flowable</groupId>
        <artifactId>flowable-spring-boot-starter-process</artifactId>
        <version>${flowable.version}</version>
      </dependency>
      <dependency>
        <groupId>org.flowable</groupId>
        <artifactId>flowable-spring-boot-starter-actuator</artifactId>
        <version>${flowable.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-common</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-excel</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct</artifactId>
        <version>${mapstruct.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct-jdk8</artifactId>
        <version>${mapstruct.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct-processor</artifactId>
        <version>${mapstruct.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-all</artifactId>
        <version>${hutool.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.idev.excel</groupId>
        <artifactId>fastexcel</artifactId>
        <version>${fastexcel.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tika</groupId>
        <artifactId>tika-core</artifactId>
        <version>${tika-core.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.velocity</groupId>
        <artifactId>velocity-engine-core</artifactId>
        <version>${velocity.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>${fastjson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>transmittable-thread-local</artifactId>
        <version>${transmittable-thread-local.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-net</groupId>
        <artifactId>commons-net</artifactId>
        <version>${commons-net.version}</version>
      </dependency>
      <dependency>
        <groupId>com.jcraft</groupId>
        <artifactId>jsch</artifactId>
        <version>${jsch.version}</version>
      </dependency>
      <dependency>
        <groupId>com.anji-plus</groupId>
        <artifactId>captcha-spring-boot-starter</artifactId>
        <version>${anji-plus-captcha.version}</version>
      </dependency>
      <dependency>
        <groupId>org.lionsoul</groupId>
        <artifactId>ip2region</artifactId>
        <version>${ip2region.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jsoup</groupId>
        <artifactId>jsoup</artifactId>
        <version>${jsoup.version}</version>
      </dependency>
      <dependency>
        <groupId>software.amazon.awssdk</groupId>
        <artifactId>s3</artifactId>
        <version>${awssdk.version}</version>
      </dependency>
      <dependency>
        <groupId>me.zhyd.oauth</groupId>
        <artifactId>JustAuth</artifactId>
        <version>${justauth.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xkcoding.justauth</groupId>
        <artifactId>justauth-spring-boot-starter</artifactId>
        <version>${justauth-starter.version}</version>
        <exclusions>
          <exclusion>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.github.binarywang</groupId>
        <artifactId>weixin-java-pay</artifactId>
        <version>${weixin-java.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.binarywang</groupId>
        <artifactId>wx-java-mp-spring-boot-starter</artifactId>
        <version>${weixin-java.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.binarywang</groupId>
        <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>
        <version>${weixin-java.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jeecgframework.jimureport</groupId>
        <artifactId>jimureport-spring-boot-starter</artifactId>
        <version>${jimureport.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jeecgframework.jimureport</groupId>
        <artifactId>jimubi-spring-boot-starter</artifactId>
        <version>${jimubi.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.pf4j</groupId>
        <artifactId>pf4j-spring</artifactId>
        <version>${pf4j-spring.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>io.vertx</groupId>
        <artifactId>vertx-core</artifactId>
        <version>${vertx.version}</version>
      </dependency>
      <dependency>
        <groupId>io.vertx</groupId>
        <artifactId>vertx-web</artifactId>
        <version>${vertx.version}</version>
      </dependency>
      <dependency>
        <groupId>io.vertx</groupId>
        <artifactId>vertx-mqtt</artifactId>
        <version>${vertx.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.paho</groupId>
        <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
        <version>${mqtt.version}</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-core</artifactId>
        <version>${logback.version}</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
        <version>${logback.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
