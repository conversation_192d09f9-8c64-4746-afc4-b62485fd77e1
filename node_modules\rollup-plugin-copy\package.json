{"name": "rollup-plugin-copy", "description": "Copy files and folders using Rollup", "version": "3.5.0", "author": "<PERSON> <<EMAIL>>", "repository": "vladshcherbin/rollup-plugin-copy", "main": "dist/index.commonjs.js", "module": "dist/index.module.js", "types": "index.d.ts", "scripts": {"clean": "rimraf coverage dist", "build": "rollup -c", "lint": "eslint src tests", "postpublish": "yarn clean", "prepublishOnly": "yarn lint && yarn test && yarn clean && yarn build", "test": "jest"}, "dependencies": {"@types/fs-extra": "^8.0.1", "colorette": "^1.1.0", "fs-extra": "^8.1.0", "globby": "10.0.1", "is-plain-object": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.12.17", "@babel/preset-env": "^7.12.17", "babel-jest": "^25.5.1", "codecov": "^3.6.1", "eslint": "6.5.1", "eslint-config-airbnb-base": "^14.0.0", "eslint-plugin-import": "^2.20.0", "jest": "^25.5.4", "replace-in-file": "^5.0.2", "rimraf": "^3.0.0", "rollup": "^1.29.0", "rollup-plugin-auto-external": "^2.0.0", "rollup-plugin-babel": "^4.3.3"}, "files": ["dist", "index.d.ts", "readme.md"], "keywords": ["rollup", "rollup-plugin", "copy", "cp", "asset", "assets", "file", "files", "folder", "folders", "glob"], "engines": {"node": ">=8.3"}, "license": "MIT"}